# Whenever you update version here, PROTOBUF_VERSION should be updated
# in scripts/generate_proto_stubs.sh and vice-versa.
version = "4.25.*"
upstream_repository = "https://github.com/protocolbuffers/protobuf"
extra_description = "Generated using [mypy-protobuf==3.6.0](https://github.com/nipunn1313/mypy-protobuf/tree/v3.6.0) on [protobuf v25.3](https://github.com/protocolbuffers/protobuf/releases/tag/v25.3) (python protobuf==4.25.3)"
partial_stub = true

[tool.stubtest]
ignore_missing_stub = true
