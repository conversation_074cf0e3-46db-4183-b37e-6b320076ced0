# .github/workflows/sync-main-to-sandbox-production.yml

name: Sync main to sandbox-production

on:
  push:
    branches:
      - main

permissions:
  contents: write
  pull-requests: write

jobs:
  sync:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all history so we can force push

      - name: Set up Git
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@livekit.io'

      - name: Sync to sandbox-production
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git checkout sandbox-production || git checkout -b sandbox-production
          git merge --strategy-option theirs main
          git push origin sandbox-production
